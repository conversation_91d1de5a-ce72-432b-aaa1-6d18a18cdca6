<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Expenses</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Project Expense
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update expense record for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Expense Form -->
<div class="card">
    <div class="card-header">
        ✏️ Edit Expense Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/edit') ?>" enctype="multipart/form-data" class="expense-edit-form">
            <?= csrf_field() ?>

            <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Description -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="description" class="form-label">
                            Description <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <textarea id="description"
                                  name="description"
                                  class="form-input"
                                  style="border: 2px solid var(--brand-danger); min-height: 100px; resize: vertical;"
                                  placeholder="Detailed description of the expense..."
                                  required><?= old('description', $expense['description']) ?></textarea>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Detailed description of this expense (max 255 characters)
                        </small>
                        <?php if (isset($errors['description'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['description']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Amount Paid -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="amount_paid" class="form-label">
                            Amount Paid <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <div style="position: relative;">
                            <span style="position: absolute; left: var(--spacing-sm); top: 50%; transform: translateY(-50%); color: var(--text-muted); font-weight: 600;">$</span>
                            <input type="number"
                                   id="amount_paid"
                                   name="amount_paid"
                                   class="form-input"
                                   style="border: 2px solid var(--brand-danger); padding-left: var(--spacing-lg);"
                                   value="<?= old('amount_paid', $expense['amount_paid']) ?>"
                                   placeholder="0.00"
                                   step="0.01"
                                   min="0.01"
                                   required>
                        </div>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Amount paid for this expense
                        </small>
                        <?php if (isset($errors['amount_paid'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['amount_paid']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Payment Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="paid_on" class="form-label">
                            Payment Date <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="date"
                               id="paid_on"
                               name="paid_on"
                               class="form-input"
                               style="border: 2px solid var(--brand-danger);"
                               value="<?= old('paid_on', $expense['paid_on']) ?>"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Date when the payment was made
                        </small>
                        <?php if (isset($errors['paid_on'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['paid_on']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Milestone (Optional) -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="milestone_id" class="form-label">
                            Related Milestone
                        </label>
                        <select id="milestone_id"
                                name="milestone_id"
                                class="form-input"
                                style="border: 2px solid var(--brand-secondary);">
                            <option value="">Select milestone (optional)</option>
                            <?php foreach ($milestones as $milestone): ?>
                                <option value="<?= $milestone['id'] ?>" <?= old('milestone_id', $expense['milestone_id']) == $milestone['id'] ? 'selected' : '' ?>>
                                    <?= esc($milestone['milestone_code']) ?> - <?= esc($milestone['title']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Link this expense to a specific project milestone (optional)
                        </small>
                        <?php if (isset($errors['milestone_id'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['milestone_id']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Current File Display -->
                    <?php if (!empty($expense['file_path'])): ?>
                        <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                            <label class="form-label">Current Supporting Document</label>
                            <div style="background: var(--bg-tertiary); padding: var(--spacing-md); border-radius: var(--radius-sm); border: 1px solid var(--border-color);">
                                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                    <span style="font-size: 1.5rem;">📎</span>
                                    <div style="flex: 1;">
                                        <div style="font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">
                                            <?= basename($expense['file_path']) ?>
                                        </div>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                                            Uploaded: <?= date('M d, Y', strtotime($expense['created_at'])) ?>
                                        </div>
                                    </div>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/download') ?>" 
                                       class="btn btn-sm btn-outline-primary" 
                                       style="font-size: 0.75rem; padding: var(--spacing-xs) var(--spacing-sm);">
                                        📥 Download
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Supporting File (Optional for edit) -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="supporting_file" class="form-label">
                            Replace Supporting Document
                        </label>
                        <input type="file"
                               id="supporting_file"
                               name="supporting_file"
                               class="form-input"
                               style="border: 2px solid var(--brand-secondary);"
                               accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.xls,.xlsx">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Upload new file to replace current document (optional) - Max 15MB
                        </small>
                        <?php if (isset($errors['supporting_file'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['supporting_file']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- File Upload Info -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-md); border-radius: var(--radius-sm); border-left: 4px solid var(--brand-primary);">
                        <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 0.875rem; color: var(--text-primary);">📎 File Upload Info</h4>
                        <ul style="margin: 0; padding-left: var(--spacing-md); font-size: 0.75rem; color: var(--text-muted);">
                            <li>Leave empty to keep current file</li>
                            <li>Maximum file size: 15MB</li>
                            <li>Accepted formats: PDF, DOC, DOCX, JPG, PNG, GIF, XLS, XLSX</li>
                            <li>New file will replace the existing one</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses') ?>" class="btn btn-secondary btn-mobile">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">Cancel</span>
                </a>
                <button type="submit" class="btn btn-primary btn-mobile">
                    <span class="btn-icon">✏️</span>
                    <span class="btn-text">Update Expense</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 1rem;">Validation Errors</h4>
        <ul style="margin: 0; padding-left: var(--spacing-md);">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li style="font-size: 0.875rem;"><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 44px;
    border: 2px solid var(--brand-secondary); /* Default green for optional fields */
}

/* Required fields have red outline */
.form-input[required] {
    border: 2px solid var(--brand-danger);
}

.form-input:focus {
    outline: none;
    border-width: 2px;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Enhanced select dropdowns */
select.form-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Enhanced textareas */
textarea.form-input {
    resize: vertical;
    min-height: 100px;
}

/* File input styling */
input[type="file"].form-input {
    padding: var(--spacing-sm);
    border: 2px dashed var(--brand-secondary);
    background: var(--bg-secondary);
}

input[type="file"].form-input:focus {
    border-style: solid;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .form-input {
        min-height: 48px;
        font-size: 16px;
        padding: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    .form-actions {
        gap: var(--spacing-sm);
    }

    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});

// Form validation
document.querySelector('.expense-edit-form').addEventListener('submit', function(e) {
    const description = document.getElementById('description').value.trim();
    const amountPaid = document.getElementById('amount_paid').value;
    const paidOn = document.getElementById('paid_on').value;
    const supportingFile = document.getElementById('supporting_file').files[0];

    if (!description || !amountPaid || !paidOn || parseFloat(amountPaid) <= 0) {
        e.preventDefault();
        alert('Please fill in all required fields with valid values.');
        return false;
    }

    // Check file size if new file is selected (15MB = 15 * 1024 * 1024 bytes)
    if (supportingFile && supportingFile.size > 15 * 1024 * 1024) {
        e.preventDefault();
        alert('File size must be less than 15MB.');
        return false;
    }
});
</script>

<?= $this->endSection() ?>
