<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectExpenseModel;
use App\Models\ProjectMilestoneModel;

/**
 * Admin Project Expense Controller
 * 
 * Handles CRUD operations for project expenses in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectExpenseController extends BaseController
{
    protected $projectModel;
    protected $projectExpenseModel;
    protected $projectMilestoneModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectExpenseModel = new ProjectExpenseModel();
        $this->projectMilestoneModel = new ProjectMilestoneModel();
    }

    /**
     * Show expenses list - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get expenses for this project with milestone info
        $expenses = $this->projectExpenseModel->getExpensesWithMilestone($projectId);

        // Get expense statistics
        $expenseStats = $this->projectExpenseModel->getExpenseStatistics($projectId);

        $data = [
            'title' => 'Project Expenses - PROMIS Admin',
            'page_title' => 'Project Expenses',
            'project' => $project,
            'expenses' => $expenses,
            'expenseStats' => $expenseStats
        ];

        return view('admin/expenses_list', $data);
    }

    /**
     * Show create expense form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get project milestones for dropdown
        $milestones = $this->projectMilestoneModel->where('project_id', $projectId)
                                                 ->where('deleted_at', null)
                                                 ->orderBy('title', 'ASC')
                                                 ->findAll();

        $data = [
            'title' => 'Create Project Expense - PROMIS Admin',
            'page_title' => 'Create Project Expense',
            'project' => $project,
            'milestones' => $milestones
        ];

        return view('admin/expenses_create', $data);
    }

    /**
     * Store expense - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'description' => 'required|max_length[255]',
            'amount_paid' => 'required|decimal|greater_than[0]',
            'paid_on' => 'required|valid_date',
            'milestone_id' => 'permit_empty|integer',
            'supporting_file' => 'uploaded[supporting_file]|max_size[supporting_file,15360]' // 15MB
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $file = $this->request->getFile('supporting_file');
        
        if (!$file->isValid()) {
            return redirect()->back()->withInput()->with('error', 'File upload failed: ' . $file->getErrorString());
        }

        // Generate unique filename
        $fileName = $file->getRandomName();
        $uploadPath = ROOTPATH . 'public/uploads/project_expenses/';
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Move file to upload directory
        if (!$file->move($uploadPath, $fileName)) {
            return redirect()->back()->withInput()->with('error', 'Failed to upload file.');
        }

        // Prepare expense data
        $expenseData = [
            'project_id' => $projectId,
            'milestone_id' => $this->request->getPost('milestone_id') ?: null,
            'description' => $this->request->getPost('description'),
            'amount_paid' => $this->request->getPost('amount_paid'),
            'paid_on' => $this->request->getPost('paid_on'),
            'file_path' => 'public/uploads/project_expenses/' . $fileName,
            'created_by' => $adminUserId
        ];

        try {
            $expenseId = $this->projectExpenseModel->insert($expenseData);

            if ($expenseId) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/expenses'))
                               ->with('success', 'Expense record created successfully.');
            } else {
                // Delete uploaded file if database insert failed
                if (file_exists($uploadPath . $fileName)) {
                    unlink($uploadPath . $fileName);
                }
                return redirect()->back()->withInput()->with('errors', $this->projectExpenseModel->errors());
            }
        } catch (\Exception $e) {
            // Delete uploaded file if exception occurred
            if (file_exists($uploadPath . $fileName)) {
                unlink($uploadPath . $fileName);
            }
            return redirect()->back()->withInput()->with('error', 'Error creating expense: ' . $e->getMessage());
        }
    }

    /**
     * Show expense details - GET request
     */
    public function show($projectId, $expenseId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get expense with milestone info
        $expense = $this->projectExpenseModel->select('project_expenses.*, project_milestones.title as milestone_title, project_milestones.milestone_code')
                                           ->join('project_milestones', 'project_milestones.id = project_expenses.milestone_id', 'left')
                                           ->where('project_expenses.id', $expenseId)
                                           ->where('project_expenses.project_id', $projectId)
                                           ->where('project_expenses.deleted_at', null)
                                           ->first();

        if (!$expense) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/expenses'))
                           ->with('error', 'Expense not found.');
        }

        $data = [
            'title' => 'Expense Details - PROMIS Admin',
            'page_title' => 'Expense Details',
            'project' => $project,
            'expense' => $expense
        ];

        return view('admin/expenses_show', $data);
    }

    /**
     * Show edit expense form - GET request
     */
    public function edit($projectId, $expenseId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get expense
        $expense = $this->projectExpenseModel->where('id', $expenseId)
                                           ->where('project_id', $projectId)
                                           ->where('deleted_at', null)
                                           ->first();

        if (!$expense) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/expenses'))
                           ->with('error', 'Expense not found.');
        }

        // Get project milestones for dropdown
        $milestones = $this->projectMilestoneModel->where('project_id', $projectId)
                                                 ->where('deleted_at', null)
                                                 ->orderBy('title', 'ASC')
                                                 ->findAll();

        $data = [
            'title' => 'Edit Project Expense - PROMIS Admin',
            'page_title' => 'Edit Project Expense',
            'project' => $project,
            'expense' => $expense,
            'milestones' => $milestones
        ];

        return view('admin/expenses_edit', $data);
    }

    /**
     * Update expense - POST request
     */
    public function update($projectId, $expenseId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get existing expense
        $expense = $this->projectExpenseModel->where('id', $expenseId)
                                           ->where('project_id', $projectId)
                                           ->where('deleted_at', null)
                                           ->first();

        if (!$expense) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/expenses'))
                           ->with('error', 'Expense not found.');
        }

        // Validation rules
        $rules = [
            'description' => 'required|max_length[255]',
            'amount_paid' => 'required|decimal|greater_than[0]',
            'paid_on' => 'required|valid_date',
            'milestone_id' => 'permit_empty|integer'
        ];

        // Add file validation if new file is uploaded
        if ($this->request->getFile('supporting_file') && $this->request->getFile('supporting_file')->isValid()) {
            $rules['supporting_file'] = 'max_size[supporting_file,15360]'; // 15MB
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare update data
        $updateData = [
            'milestone_id' => $this->request->getPost('milestone_id') ?: null,
            'description' => $this->request->getPost('description'),
            'amount_paid' => $this->request->getPost('amount_paid'),
            'paid_on' => $this->request->getPost('paid_on'),
            'updated_by' => $adminUserId
        ];

        // Handle file upload if new file provided
        $file = $this->request->getFile('supporting_file');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Generate unique filename
            $fileName = $file->getRandomName();
            $uploadPath = ROOTPATH . 'public/uploads/project_expenses/';
            
            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Move new file
            if ($file->move($uploadPath, $fileName)) {
                // Delete old file
                if (!empty($expense['file_path']) && file_exists(ROOTPATH . $expense['file_path'])) {
                    unlink(ROOTPATH . $expense['file_path']);
                }

                // Update file information
                $updateData['file_path'] = 'public/uploads/project_expenses/' . $fileName;
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to upload new file.');
            }
        }

        try {
            $result = $this->projectExpenseModel->update($expenseId, $updateData);

            if ($result) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/expenses'))
                               ->with('success', 'Expense updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectExpenseModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating expense: ' . $e->getMessage());
        }
    }

    /**
     * Delete expense - POST request
     */
    public function delete($projectId, $expenseId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get expense
        $expense = $this->projectExpenseModel->where('id', $expenseId)
                                           ->where('project_id', $projectId)
                                           ->where('deleted_at', null)
                                           ->first();

        if (!$expense) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/expenses'))
                           ->with('error', 'Expense not found.');
        }

        try {
            // Soft delete the expense
            $result = $this->projectExpenseModel->update($expenseId, [
                'deleted_by' => $adminUserId,
                'deleted_at' => date('Y-m-d H:i:s')
            ]);

            if ($result) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/expenses'))
                               ->with('success', 'Expense deleted successfully.');
            } else {
                return redirect()->back()->with('error', 'Failed to delete expense.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error deleting expense: ' . $e->getMessage());
        }
    }

    /**
     * Download expense file - GET request
     */
    public function download($projectId, $expenseId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get expense
        $expense = $this->projectExpenseModel->where('id', $expenseId)
                                           ->where('project_id', $projectId)
                                           ->where('deleted_at', null)
                                           ->first();

        if (!$expense || empty($expense['file_path'])) {
            return redirect()->back()->with('error', 'File not found.');
        }

        $filePath = ROOTPATH . $expense['file_path'];
        
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'File not found on server.');
        }

        return $this->response->download($filePath, null);
    }
}
